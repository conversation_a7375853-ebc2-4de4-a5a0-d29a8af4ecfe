<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Edunetra') }} - @yield('title', 'Visual Impairment Solutions: Inclusive Opportunities and navigation for English Learning')</title>

        <!-- Bootstrap CSS -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">

        <!-- Google Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

        <!-- Custom Styles -->
        <style>
            :root {
                --edunetra-yellow: #FFD700;
                --edunetra-dark: #2D3748;
                --edunetra-light: #F7FAFC;
            }

            body {
                font-family: 'Inter', sans-serif;
                line-height: 1.6;
            }

            .bg-edunetra-yellow {
                background-color: var(--edunetra-yellow) !important;
            }

            .text-edunetra-yellow {
                color: var(--edunetra-yellow) !important;
            }

            .bg-edunetra-dark {
                background-color: var(--edunetra-dark) !important;
            }

            .text-edunetra-dark {
                color: var(--edunetra-dark) !important;
            }

            /* Hero Slider Styles */
            .hero-slider-bg {
                min-height: 100vh;
                position: relative;
                overflow: hidden;
            }

            .hero-slide {
                min-height: 100vh;
                background-size: cover;
                background-position: center;
                background-repeat: no-repeat;
            }

            .hero-content {
                z-index: 10;
            }

            .carousel-fade .carousel-item {
                opacity: 0;
                transition-duration: 1s;
                transition-property: opacity;
            }

            .carousel-fade .carousel-item.active {
                opacity: 1;
            }

            /* Info Cards Styles */
            .info-card {
                border-radius: 15px;
                transition: all 0.3s ease;
                min-height: 120px;
                display: flex;
                align-items: center;
            }

            .info-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            }

            .info-card-link:hover .info-card {
                transform: translateY(-5px);
            }

            .info-card-body {
                width: 100%;
            }

            .navbar-brand {
                font-weight: 700;
                font-size: 1.5rem;
            }

            .btn-edunetra {
                background-color: var(--edunetra-yellow);
                border-color: var(--edunetra-yellow);
                color: #000;
                font-weight: 600;
                transition: all 0.3s ease;
            }

            .btn-edunetra:hover {
                background-color: #E6C200;
                border-color: #E6C200;
                color: #000;
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
            }

            .btn-outline-edunetra {
                border-color: var(--edunetra-yellow);
                color: var(--edunetra-yellow);
                font-weight: 600;
                transition: all 0.3s ease;
            }

            .btn-outline-edunetra:hover {
                background-color: var(--edunetra-yellow);
                border-color: var(--edunetra-yellow);
                color: #000;
                transform: translateY(-2px);
            }

            .card {
                transition: all 0.3s ease;
                border: none;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }

            .card:hover {
                transform: translateY(-5px);
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            }

            .section-padding {
                padding: 80px 0;
            }

            .text-gradient {
                background: linear-gradient(135deg, var(--edunetra-yellow), #FFA500);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }

            /* Course Card Styles */
            .course-card {
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
            }

            .course-card:hover {
                transform: translateY(-8px);
                box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            }

            .z-index-1 {
                z-index: 1;
            }

            /* Product Card Styles */
            .product-card {
                transition: all 0.3s ease;
                border-radius: 20px;
                overflow: hidden;
            }

            .product-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            }

            .product-image {
                border-radius: 20px 0 0 20px;
            }

            /* Activity Card Styles */
            .activity-card {
                transition: all 0.3s ease;
                border-radius: 15px;
                overflow: hidden;
            }

            .activity-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 30px rgba(0,0,0,0.1);
            }

            .activity-image {
                border-radius: 15px 15px 0 0;
            }

            /* Animation for bounce effect */
            .animate-bounce {
                animation: bounce 2s infinite;
            }

            @keyframes bounce {
                0%, 20%, 50%, 80%, 100% {
                    transform: translateY(0);
                }
                40% {
                    transform: translateY(-10px);
                }
                60% {
                    transform: translateY(-5px);
                }
            }

            /* Search form enhancement */
            .search-form .form-control:focus {
                border-color: var(--edunetra-yellow);
                box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
            }

            /* About Page Specific Styles */
            .program-card {
                transition: all 0.3s ease;
                border-radius: 15px;
            }

            .program-card:hover {
                transform: translateY(-8px);
                box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            }

            .approach-steps .d-flex:hover {
                transform: translateX(10px);
                transition: all 0.3s ease;
            }

            .animate-fade-in {
                animation: fadeInUp 1s ease-out;
            }

            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .min-vh-50 {
                min-height: 50vh;
            }

            .hover-opacity-100:hover {
                opacity: 1 !important;
            }

            /* Enhanced card hover effects */
            .card:hover .bi {
                transform: scale(1.1);
                transition: transform 0.3s ease;
            }

            /* Responsive adjustments */
            @media (max-width: 768px) {
                .hero-content h1 {
                    font-size: 3rem !important;
                }

                .info-card {
                    min-height: 100px;
                }

                .product-image {
                    border-radius: 20px 20px 0 0;
                }

                .display-3 {
                    font-size: 2.5rem !important;
                }

                .display-5 {
                    font-size: 2rem !important;
                }

                .approach-steps .d-flex:hover {
                    transform: none;
                }
            }

            @media (max-width: 576px) {
                .section-padding {
                    padding: 60px 0;
                }

                .display-3 {
                    font-size: 2rem !important;
                }

                .display-5 {
                    font-size: 1.75rem !important;
                }
            }
        </style>
    </head>
    <body>
        @include('layouts.navigation')

        <!-- Page Content -->
        <main>
            @yield('content')
            {{ $slot ?? '' }}
        </main>

        @include('layouts.footer')

        <!-- Bootstrap JS -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

        <!-- Custom Scripts -->
        <script>
            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        </script>
    </body>
</html>
